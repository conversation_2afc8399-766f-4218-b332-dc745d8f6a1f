import os
import sys
import argparse
from dotenv import load_dotenv

from utils.logger import Logger

logging = Logger(__name__).get()

def handle_chat(args: argparse.Namespace) -> None:
    logging.info("Starting RAG chat server...")


def init_env() -> None:
    load_dotenv(override=True)
    os.environ["TOKENIZERS_PARALLELISM"] = "false"
    os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')
    os.environ['HF_TOKEN'] = os.getenv('HF_TOKEN', 'your-key-if-not-using-env')

def build_parser() -> argparse.ArgumentParser:
    parser = argparse.ArgumentParser(
        prog="percipio-ai-rag",
        description="Percipio AI RAG tools",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    sub = parser.add_subparsers(dest="command", required=True)

    init_env()

    # Cards
    run_p = sub.add_parser("chat", help="Handle classic chat")
    run_p.set_defaults(func = handle_chat)

    return parser

def main(argv: list[str] | None = None) -> None:
    """Entry point — parse CLI and dispatch."""
    args = build_parser().parse_args(argv)
    args.func(args)          # call the handler bound via set_defaults

if __name__ == "__main__":
    main(sys.argv[1:])