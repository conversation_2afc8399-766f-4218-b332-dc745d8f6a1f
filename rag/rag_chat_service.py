import os
from huggingface_hub import login
from openai import OpenAI
from sentence_transformers import SentenceTransformer
import chromadb

from utils.logger import Logger
from utils.constants import DB

logging = Logger(__name__).get()

class RagChatService:
    def __init__(self):
        self.openai = OpenAI()
        self.model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        # Log in to HuggingFace
        hf_token = os.environ['HF_TOKEN']
        login(hf_token, add_to_git_credential=True)
        # Retrieve collections
        logging.info(f"Retrieving article and patent collections...")
        client = chromadb.PersistentClient(path=DB)
        self.article_collection = client.get_or_create_collection('articles')
        self.patent_collection = client.get_or_create_collection('patents')

    def vector(self, query_message):
        return self.model.encode(query_message)

    def find_similar_and_reduce_duplicates(self, collection, query_message):
        results = collection.query(query_embeddings=self.vector(query_message).astype(float).tolist(), n_results=10)
        documents = results['documents'][0][:]
        metadata = results['metadatas'][0][:]
        unique_documents = []
        unique_metadata = []
        seen_titles = set()
        for doc, meta in zip(documents, metadata):
            if meta['title'] not in seen_titles:
                unique_documents.append(doc)
                unique_metadata.append(meta)
                seen_titles.add(meta['title'])
        return unique_documents, unique_metadata

    def find_similar(self, query_message):
        article_documents, article_metadata = self.find_similar_and_reduce_duplicates(self.article_collection, query_message)
        patent_documents, patent_metadata = self.find_similar_and_reduce_duplicates(self.patent_collection, query_message)
        return article_documents + patent_documents, article_metadata + patent_metadata

    def make_context(self, similars):
        message = "To provide some context, here are some other documents that might be similar to the query you need to answer. Potentially related documents:\n\n"
        for similar in similars:
            message += f"{similar}\n\n"
        return message

    def messages_for(self, query_message, similars):
        system_message = "Provide summary explanation for user prompt. Use your knowledge and knowledge from provided documents"
        user_prompt = self.make_context(similars)
        user_prompt += "And now the question for you:\n\n"
        user_prompt += query_message
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_prompt}
        ]
