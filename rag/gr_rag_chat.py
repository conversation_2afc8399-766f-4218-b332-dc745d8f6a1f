from rag.rag_chat_service import RagChatService
import gradio as gr

from utils.logger import Logger

logging = Logger(__name__).get()

class GrRagChat(RagChatService):
    def __init__(self):
        super().__init__()

    def chat(self, message, history):
        """
        Function to handle chat interactions for Gradio ChatInterface.

        Args:
            message: User's input message
            history: Chat history

        Returns:
            AI's response
        """
        reply, documents, metadata = self.gpt_4o_mini_rag(message)

        # Format the response with citations
        formatted_reply = f"{reply}\n\n---\n\n**Sources:**\n"

        # Build source list
        sources_reply = ""
        for i, (doc, meta) in enumerate(zip(documents, metadata)):
            title = meta.get('title', 'Untitled Document')
            summary = meta.get('summary', '')
            url = meta.get('url', '')

            # Create link section if URLs are available
            url_link = ""
            if url:
                first_url = url
                url_link = f" ([Link]({first_url}))"

            sources_reply += f"{i + 1}. **{title}**{url_link}\n"
            sources_reply += f"   {summary[:200]}...\n\n"

        formatted_reply += sources_reply

        return formatted_reply

    def launch(self):
        gr.ChatInterface(
            self.chat,
            title = "Research Assistant",
            description = "Ask questions about scientific articles in our knowledge base. The assistant will use RAG to find relevant information.",
            examples = [
                "How can spermidine maintain cellular health?",
                "What is the role of polyamines in cellular processes?",
                "Explain the relationship between spermidine and autophagy.",
                "How do polyamines affect aging?",
                "What are the effects of spermidine on lifespan?"
            ],
            theme = "soft"
        ).launch(share = True)