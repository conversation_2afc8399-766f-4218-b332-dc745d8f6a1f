import requests
import pickle
from cassandra.cluster import Cluster
from article_utils import Item

def retrieve_documents(search_term, max_docs=10000, batch_size=10):
    """
    Retrieve documents from Elasticsearch and Cassandra using pagination.
    
    Args:
        search_term: The term to search for in Elasticsearch
        max_docs: Maximum number of documents to retrieve
        batch_size: Number of documents to retrieve per batch
        
    Returns:
        List of Item objects representing the retrieved articles
    """
    # Elasticsearch endpoint
    url = "http://********:9201/doc_v1_2,doc_v1_3,doc_v1_4/_search"
    
    # Cassandra connection
    cassandra_host = "********"
    cassandra_port = 9043
    keyspace = "doc_v1"
    a_table = "doi_doc"
    p_table = "patent_doc"
    
    cluster = Cluster([cassandra_host], port=cassandra_port)
    session = cluster.connect()
    session.set_keyspace(keyspace)
    
    # Process documents in batches
    processed_count = 0
    items = []  # Store all retrieved items
    
    for from_idx in range(0, max_docs, batch_size):
        # Query body with pagination
        query = {
            "from": from_idx,
            "size": batch_size,
            "query": {
                "bool": {
                    "must": [
                        {
                            "query_string": {
                                "query": f"lemmas:\"{search_term}\""
                            }
                        }
                    ],
                    "should": [],
                    "must_not": []
                }
            }
        }
        
        # Send POST request
        response = requests.post(url, json=query)
        
        # Check for success
        if response.status_code == 200:
            data = response.json()
            hits = data.get("hits", {}).get("hits", [])
            
            # If no more results, break the loop
            if not hits:
                print(f"No more results found after {processed_count} documents.")
                break
                
            print(f"\nProcessing batch {from_idx//batch_size + 1} (documents {from_idx+1}-{from_idx+len(hits)})")
            
            for hit in hits:
                source = hit.get("_source", {})
                doc_type = source.get("type")
                doc_id = source.get("id")
                print(f"\nES Source ID: {doc_id} (type: {doc_type})")
                
                if doc_type == 'a':
                    # Query Cassandra
                    try:
                        cass_query = f"SELECT * FROM {a_table} WHERE id = %s"
                        result = session.execute(cass_query, [doc_id])
                        cass_result = result.one()
                        if cass_result:
                            print(f"Processing Cassandra document: {doc_id}")
                            
                            # Create Item from Cassandra row and add to items list
                            item = Item.from_cassandra_row(cass_result)

                            if item.title and item.summary:

                                # Add to list
                                items.append(item)
                            
                                # Generate markdown file
                                item.to_markdown()
                        else:
                            print(f"No matching record found in Cassandra for ID: {doc_id}")
                    except Exception as e:
                        print(f"Error querying Cassandra: {e}")
                else:
                    print(f"Skipping Cassandra query for document type: {doc_type}")
                
                processed_count += 1
        else:
            print(f"Error: {response.status_code} - {response.text}")
            break
    
    print(f"\nCompleted processing {processed_count} documents.")
    print(f"Retrieved {len(items)} articles.")
    cluster.shutdown()
    
    return items

if __name__ == "__main__":
    # Example usage
    search_term = "spermidine"
    items = retrieve_documents(search_term, max_docs=10000, batch_size=10)
    
    # You can now work with the items list
    print(f"\nSample of retrieved items:")
    for i, item in enumerate(items[:3]):  # Show first 3 items
        print(f"{i+1}. {item.title} ({item.year}) - {len(item.authors)} authors")

    # Save all items to a pickle file
    pickle_file = "articles.pkl"
    with open(pickle_file, 'wb') as f:
        pickle.dump(items, f)
    print(f"\nSaved {len(items)} articles to {pickle_file}")
