import os
from tqdm import tqdm
from dotenv import load_dotenv
from huggingface_hub import login
import pickle
from sentence_transformers import SentenceTransformer
import chromadb

def description(item):
    text = item.title + ' ' + item.summary
    return text

def loadItems(pickle_file):
    print(f"Loading data from {pickle_file}...")
    with open(pickle_file, 'rb') as file:
        items = pickle.load(file)
    return items

# environment

load_dotenv(override=True)
os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')
os.environ['HF_TOKEN'] = os.getenv('HF_TOKEN', 'your-key-if-not-using-env')
DB = "documents_vectorstore"

# Log in to HuggingFace

hf_token = os.environ['HF_TOKEN']
login(hf_token, add_to_git_credential=True)

# Load all items from pickle files
articleItems = loadItems("articles.pkl")
patentItems = loadItems("patents.pkl")

print(f"Loaded {len(articleItems)} article items and {len(patentItems)} patent items.")

print(f"Creating chromadb")

model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')

client = chromadb.PersistentClient(path=DB)

# Check if the collection exists and delete it if it does
collection_name = "articles"
existing_collection_names = [collection.name for collection in client.list_collections()]
if collection_name in existing_collection_names:
    client.delete_collection(collection_name)
    print(f"Deleted existing collection: {collection_name}")

collection = client.create_collection(collection_name)

print(f"Loading article items to vector store.")
for i in tqdm(range(0, len(articleItems), 1000)):
    documents = [description(item) for item in articleItems[i: i + 1000]]
    vectors = model.encode(documents).astype(float).tolist()
    metadata = [{"id": item.id, "title": item.title, "summary": item.summary, "url": item.urls.empty if "" else item.urls[0]} for item in articleItems[i: i + 1000]]
    ids = [f"doc_{j}" for j in range(i, i+metadata.__len__())]
    collection.add(
        ids=ids,
        documents=documents,
        embeddings=vectors,
        metadatas=metadata
    )

# Check if the collection exists and delete it if it does
collection_name = "patents"
existing_collection_names = [collection.name for collection in client.list_collections()]
if collection_name in existing_collection_names:
    client.delete_collection(collection_name)
    print(f"Deleted existing collection: {collection_name}")

collection = client.create_collection(collection_name)

print(f"Loading patent items to vector store.")
for i in tqdm(range(0, len(patentItems), 10)):
    documents = [description(item) for item in patentItems[i: i + 10]]
    vectors = model.encode(documents).astype(float).tolist()
    metadata = [{"id": item.id, "title": item.title, "summary": item.summary, "url": f"https://worldwide.espacenet.com/patent/search?q="+ item.id} for item in patentItems[i: i + 10]]
    ids = [f"doc_{j}" for j in range(i, i+metadata.__len__())]
    collection.add(
        ids=ids,
        documents=documents,
        embeddings=vectors,
        metadatas=metadata
    )








